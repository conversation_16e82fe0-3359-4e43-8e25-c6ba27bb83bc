/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: '<PERSON>', sans-serif;
  font-size: 18px;
  line-height: 1.6;
  color: hsl(230, 29%, 20%);
  overflow-x: hidden;
}

/* Header and Navigation */
.header {
  position: relative;
  z-index: 1000;
}

.nav {
  padding: 2rem 0;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo img {
  height: 24px;
}

.nav-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  z-index: 1001;
}

.close-icon {
  display: none;
}

.nav-menu {
  display: flex;
  list-style: none;
  align-items: center;
  gap: 2.5rem;
}

.nav-link {
  text-decoration: none;
  color: hsl(230, 29%, 20%);
  font-family: '<PERSON> Condensed', sans-serif;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: hsl(0, 100%, 68%);
}

.nav-divider {
  width: 6px;
  height: 6px;
  background-color: hsl(230, 29%, 20%);
  border-radius: 50%;
  opacity: 0.3;
}

.nav-login {
  color: hsl(230, 29%, 20%);
  opacity: 0.5;
}

/* Main content */
.main {
  position: relative;
  min-height: 80vh;
  display: flex;
  align-items: center;
}

.main::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 400px;
  background-color: hsl(207, 33%, 95%);
  border-radius: 0 0 0 60px;
  z-index: -1;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-content {
  max-width: 500px;
}

.hero-badge {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.badge {
  background-color: hsl(230, 29%, 20%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-family: 'Barlow Condensed', sans-serif;
  font-weight: 700;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.hero-subtitle {
  color: hsl(230, 29%, 20%);
  font-family: 'Barlow Condensed', sans-serif;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 4px;
  opacity: 0.5;
}

.hero-title {
  font-family: 'Barlow Condensed', sans-serif;
  font-weight: 700;
  font-size: 4rem;
  line-height: 1;
  text-transform: uppercase;
  letter-spacing: -2px;
  margin-bottom: 1.5rem;
  color: hsl(230, 29%, 20%);
}

.hero-description {
  font-size: 18px;
  color: hsl(230, 29%, 20%);
  opacity: 0.7;
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.hero-cta {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.cta-button {
  background-color: hsl(0, 100%, 68%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 6px;
  font-family: 'Barlow Condensed', sans-serif;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-button:hover {
  background-color: hsl(0, 100%, 58%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 61, 84, 0.3);
}

.cta-text {
  color: hsl(230, 29%, 20%);
  font-family: 'Barlow Condensed', sans-serif;
  text-transform: uppercase;
  letter-spacing: 4px;
  font-size: 14px;
  opacity: 0.5;
}

.hero-image {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-image img {
  max-width: 100%;
  height: auto;
  position: relative;
  z-index: 1;
}

/* Attribution */
.attribution {
  font-size: 11px;
  text-align: center;
  padding: 2rem;
  color: hsl(230, 29%, 20%);
  opacity: 0.7;
}

.attribution a {
  color: hsl(228, 45%, 44%);
  text-decoration: none;
}

.attribution a:hover {
  text-decoration: underline;
}

/* Mobile styles */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 1.5rem;
  }
  
  .nav-toggle {
    display: block;
  }
  
  .nav-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 70%;
    height: 100vh;
    background-color: white;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 6rem 2rem 2rem;
    gap: 2rem;
    transition: right 0.3s ease;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  }
  
  .nav-menu.active {
    right: 0;
  }
  
  .nav-toggle.active .hamburger-icon {
    display: none;
  }
  
  .nav-toggle.active .close-icon {
    display: block;
  }
  
  .nav-item {
    width: 100%;
  }
  
  .nav-link {
    font-size: 18px;
    display: block;
    width: 100%;
  }
  
  .nav-divider {
    width: 100%;
    height: 1px;
    border-radius: 0;
    margin: 0.5rem 0;
  }
  
  .main::before {
    width: 60%;
    height: 300px;
    border-radius: 0 0 0 40px;
  }
  
  .hero-container {
    grid-template-columns: 1fr;
    gap: 3rem;
    padding: 0 1.5rem;
    text-align: center;
  }
  
  .hero-content {
    order: 2;
    max-width: none;
  }
  
  .hero-image {
    order: 1;
  }
  
  .hero-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }
  
  .hero-cta {
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
  }
  
  .cta-text {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .main::before {
    width: 70%;
    height: 250px;
  }
}
