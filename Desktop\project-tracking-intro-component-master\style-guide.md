# Front-end Style Guide

## Layout

The designs were created to the following widths:

- Mobile: 375px
- Desktop: 1440px

> 💡 These are just the design sizes. Ensure content is responsive and meets WCAG requirements by testing the full range of screen sizes from 320px to large screens.

## Colors

### Primary

- Red 400: hsl(0, 100%, 68%)

### Neutral

- Blue 950: hsl(230, 29%, 20%)
- Blue 100: hsl(207, 33%, 95%)

## Typography

### Body Copy

- Font size: 18px

### Headings, Call-to-actions, Navigation

- Family: [<PERSON> Condensed](https://fonts.google.com/specimen/Barlow+Condensed)
- Weights: 400, 700

### Body

- Family: [<PERSON>](https://fonts.google.com/specimen/Barlow)
- Weights: 400

> 💎 [Upgrade to Pro](https://www.frontendmentor.io/pro?ref=style-guide) for design file access to see all design details and get hands-on experience using a professional workflow with tools like Figma.
